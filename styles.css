/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
}

.logo {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.8rem;
  font-weight: 700;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo i {
  color: var(--accent);
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* .nav-logo h1 {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.8rem;
  font-weight: 700;
} */

.language-switcher {
  display: flex;
  gap: 0.5rem;
}

.lang-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #ff6b6b;
  background: transparent;
  color: #ff6b6b;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  min-height: 44px; /* Better touch target for mobile */
  display: flex;
  align-items: center;
  justify-content: center;
}

.lang-btn.active,
.lang-btn:hover {
  background: #ff6b6b;
  color: white;
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.4) 0%,
      rgba(118, 75, 162, 0.4) 100%
    ),
    url("https://images.pexels.com/photos/9756282/pexels-photo-9756282.jpeg?_gl=1*4rjmsx*_ga****************************.*_ga_8JE65Q40S6*czE3NTUwNTI4ODAkbzMkZzEkdDE3NTUwNTMzNTIkajYwJGwwJGgw")
      center/cover;
  position: relative;
  overflow: hidden;
  padding-top: 80px; /* Add padding to account for fixed navbar */
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  align-items: center;
  place-items: center;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  text-align: center;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  line-height: 1.6;
  text-align: center;
}
.hero-text {
  text-align: center;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
}

.cta-button.secondary {
  background: white;
  color: #667eea;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.cta-button.secondary:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

/* Hero Graphics */
.hero-graphic {
  position: relative;
  height: 400px;
}

.floating-hearts {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-hearts i {
  position: absolute;
  color: rgba(255, 255, 255, 0.3);
  animation: float 3s ease-in-out infinite;
}

.heart-1 {
  top: 20%;
  left: 20%;
  font-size: 1.5rem;
  animation-delay: 0s;
}

.heart-2 {
  top: 60%;
  right: 30%;
  font-size: 1rem;
  animation-delay: 1s;
}

.heart-3 {
  bottom: 30%;
  left: 60%;
  font-size: 1.2rem;
  animation-delay: 2s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.profile-cards {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-card {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: absolute;
  width: 200px;
  animation: cardFloat 4s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.card-2 {
  bottom: 20%;
  right: 10%;
  animation-delay: 2s;
}

@keyframes cardFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(2deg);
  }
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin-bottom: 1rem;
  overflow: hidden;
  border: 3px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.profile-location {
  color: #666;
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.profile-location i {
  color: #ff6b6b;
}

.profile-interests {
  display: flex;
  gap: 0.25rem;
  flex-wrap: wrap;
}

.interest-tag {
  background: #f0f0f0;
  color: #666;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Success Stories */
.success-stories {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.couple-photo {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  animation: coupleFloat 5s ease-in-out infinite;
}

.couple-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.couple-1 {
  top: 10%;
  right: 20%;
  animation-delay: 1s;
}

.couple-2 {
  bottom: 15%;
  left: 15%;
  animation-delay: 3s;
}

@keyframes coupleFloat {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(1deg);
  }
}

.success-badge {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #ff6b6b;
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.success-badge i {
  font-size: 0.6rem;
}

/* Features Section */
.features {
  padding: 6rem 0;
  background: #f8f9fa;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.features-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.feature-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-image img {
  transform: scale(1.05);
}

.feature-content {
  padding: 2rem;
  text-align: center;
}

.feature-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: -30px;
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.feature-icon i {
  font-size: 1.5rem;
  color: white;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
  margin-top: -15px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
  padding: 6rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.how-it-works::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e74c3c" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.how-it-works-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.how-it-works-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.how-it-works-subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 4rem;
  font-weight: 400;
}

/* Steps Section */
.steps-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-bottom: 5rem;
  flex-wrap: wrap;
}

.step-card {
  background: white;
  padding: 2.5rem 2rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  max-width: 280px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.step-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(231, 76, 60, 0.15);
  border-color: #e74c3c;
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.step-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1rem auto 1.5rem;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

.step-icon i {
  font-size: 2rem;
  color: white;
}

.step-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.step-card p {
  font-size: 0.95rem;
  line-height: 1.6;
  color: #6c757d;
  margin: 0;
}

.step-arrow {
  color: #e74c3c;
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(0);
  }
  40% {
    transform: translateX(10px);
  }
  60% {
    transform: translateX(5px);
  }
}

/* Map Section */
.map-section {
  margin-top: 3rem;
}

.map-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  margin-bottom: 3rem;
}

.map-image {
  width: 100%;
  height: 500px;
  object-fit: cover;
  display: block;
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, #e74c3c, #f39c12);
  border-radius: 1px;
  animation: drawLine 3s ease-in-out infinite;
}

.connection-line.line-1 {
  top: 30%;
  left: 25%;
  width: 200px;
  transform: rotate(25deg);
  animation-delay: 0s;
}

.connection-line.line-2 {
  top: 55%;
  left: 45%;
  width: 150px;
  transform: rotate(-15deg);
  animation-delay: 1s;
}

.connection-line.line-3 {
  top: 40%;
  left: 60%;
  width: 180px;
  transform: rotate(45deg);
  animation-delay: 2s;
}

@keyframes drawLine {
  0% {
    width: 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.connection-point {
  position: absolute;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.connection-point.point-1 {
  top: 25%;
  left: 20%;
}

.connection-point.point-2 {
  top: 50%;
  left: 65%;
}

.connection-point.point-3 {
  top: 65%;
  left: 40%;
}

.connection-point.point-4 {
  top: 35%;
  left: 75%;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.pulse {
  position: absolute;
  top: -5px;
  left: -5px;
  width: 60px;
  height: 60px;
  border: 2px solid #e74c3c;
  border-radius: 50%;
  animation: pulse 2s infinite;
  opacity: 0.6;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

/* .floating-heart {
  position: absolute;
  color: #e74c3c;
  font-size: 1.5rem;
  animation: float 4s ease-in-out infinite;
}

.floating-heart.heart-1 {
  top: 20%;
  left: 50%;
  animation-delay: 0s;
}

.floating-heart.heart-2 {
  top: 70%;
  left: 30%;
  animation-delay: 2s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
} */

/* Map Statistics */
.map-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem 1rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(231, 76, 60, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #e74c3c;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #e74c3c, #f39c12);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  color: #6c757d;
  font-weight: 500;
}

/* Testimonials Section */
.testimonials {
  padding: 6rem 0;
  background: #f8f9fa;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.testimonials-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 3rem;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-5px);
}

.testimonial-images {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  object-fit: cover;
}

.testimonial-avatar:first-child {
  margin-right: -15px;
  z-index: 2;
}

.testimonial-avatar:last-child {
  z-index: 1;
}

.testimonial-content p {
  font-style: italic;
  color: #555;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.testimonial-author {
  text-align: center;
}

.testimonial-author strong {
  display: block;
  color: #333;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.testimonial-author span {
  color: #ff6b6b;
  font-size: 0.9rem;
  font-weight: 500;
}

/* CTA Section */
.cta-section {
  padding: 6rem 0;
  background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.9) 0%,
      rgba(118, 75, 162, 0.9) 100%
    ),
    url("https://images.unsplash.com/photo-1541849546-216549ae216d?w=1920&h=600&fit=crop")
      center/cover;
  text-align: center;
  position: relative;
}

.cta-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 2;
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.cta-section p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
}

/* Footer */
.footer {
  background: #2c3e50;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
  margin-bottom: 2rem;
}

.footer-logo h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-logo p {
  color: rgba(255, 255, 255, 0.7);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
}

.footer-column h4 {
  margin-bottom: 1rem;
  font-weight: 600;
}

.footer-column ul {
  list-style: none;
}

.footer-column ul li {
  margin-bottom: 0.5rem;
}

.footer-column ul li a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-column ul li a:hover {
  color: #ff6b6b;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    padding: 0.75rem 0;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .nav-logo h1 {
    font-size: 1.5rem;
  }

  .hero {
    padding-top: 100px; /* Increased padding for mobile navbar */
    min-height: calc(100vh - 20px);
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2.2rem;
    line-height: 1.1;
    margin-bottom: 1rem;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.5;
  }

  .hero-graphic {
    height: 250px;
    margin-top: 1rem;
  }

  .profile-card {
    width: 140px;
    padding: 0.75rem;
  }

  .profile-avatar {
    width: 60px;
    height: 60px;
  }

  .profile-name {
    font-size: 0.8rem;
  }

  .profile-location {
    font-size: 0.7rem;
  }

  .interest-tag {
    font-size: 0.6rem;
    padding: 0.15rem 0.4rem;
  }

  .features {
    padding: 4rem 0;
  }

  .features-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
  }

  .feature-content {
    padding: 1.5rem;
  }

  .feature-image {
    height: 150px;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
    top: -25px;
  }

  .feature-icon i {
    font-size: 1.2rem;
  }

  .feature-card h3 {
    font-size: 1.2rem;
    margin-top: -10px;
  }

  .how-it-works {
    padding: 4rem 0;
  }

  .how-it-works-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .how-it-works-subtitle {
    font-size: 1rem;
    margin-bottom: 3rem;
  }

  .steps-container {
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .step-card {
    padding: 2rem 1.5rem;
    max-width: 250px;
  }

  .step-icon {
    width: 60px;
    height: 60px;
    margin: 0.5rem auto 1rem;
  }

  .step-icon i {
    font-size: 1.5rem;
  }

  .step-arrow {
    display: none;
  }

  .map-image {
    height: 350px;
  }

  .map-stats {
    gap: 1.5rem;
  }

  .stat-item {
    padding: 1.5rem 1rem;
  }

  .stat-number {
    font-size: 2rem;
  }

  .testimonials {
    padding: 4rem 0;
  }

  .testimonials-title {
    font-size: 1.8rem;
    margin-bottom: 2rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .testimonial-card {
    padding: 1.5rem;
  }

  .testimonial-avatar {
    width: 50px;
    height: 50px;
  }

  .couple-photo {
    width: 80px;
    height: 80px;
  }

  .success-badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }

  .cta-section {
    padding: 4rem 0;
  }

  .cta-section h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }

  .cta-section p {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 0.5rem 0;
  }

  .nav-logo h1 {
    font-size: 1.3rem;
  }

  .lang-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .hero {
    padding-top: 80px;
    min-height: calc(100vh - 10px);
  }

  .hero-container,
  .features-container,
  .testimonials-container,
  .cta-container,
  .footer-container {
    padding: 0 1rem;
  }

  .hero-content {
    gap: 2rem;
    padding: 1rem 0;
  }

  .hero-title {
    font-size: 1.8rem;
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
  }

  .hero-graphic {
    height: 200px;
  }

  .cta-button {
    padding: 0.75rem 1.25rem;
    font-size: 0.95rem;
    gap: 0.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-image {
    height: 120px;
  }

  .feature-content {
    padding: 1rem;
  }

  .feature-card h3 {
    font-size: 1.1rem;
  }

  .feature-card p {
    font-size: 0.9rem;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .testimonial-card {
    padding: 1rem;
  }

  .testimonial-content p {
    font-size: 0.9rem;
  }

  .profile-card {
    width: 110px;
    padding: 0.5rem;
  }

  .profile-avatar {
    width: 50px;
    height: 50px;
  }

  .profile-name {
    font-size: 0.75rem;
  }

  .profile-location {
    font-size: 0.65rem;
  }

  .interest-tag {
    font-size: 0.55rem;
    padding: 0.1rem 0.3rem;
  }

  .couple-photo {
    width: 60px;
    height: 60px;
  }

  .success-badge {
    font-size: 0.55rem;
    padding: 0.15rem 0.3rem;
  }

  .cta-section h2 {
    font-size: 1.6rem;
  }

  .cta-section p {
    font-size: 0.95rem;
  }

  .features,
  .how-it-works,
  .testimonials,
  .cta-section {
    padding: 3rem 0;
  }

  .features-title,
  .how-it-works-title,
  .testimonials-title {
    font-size: 1.6rem;
  }

  .how-it-works-subtitle {
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }

  .steps-container {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .step-card {
    padding: 1.5rem 1rem;
    max-width: 100%;
  }

  .step-number {
    width: 30px;
    height: 30px;
    font-size: 1rem;
    top: -10px;
  }

  .step-icon {
    width: 50px;
    height: 50px;
    margin: 0.5rem auto 1rem;
  }

  .step-icon i {
    font-size: 1.2rem;
  }

  .step-card h3 {
    font-size: 1.1rem;
  }

  .step-card p {
    font-size: 0.85rem;
  }

  .map-image {
    height: 250px;
  }

  .map-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1rem;
  }

  .stat-number {
    font-size: 1.8rem;
  }

  .stat-label {
    font-size: 0.9rem;
  }

  .connection-point {
    width: 40px;
    height: 40px;
  }

  .user-avatar {
    width: 35px;
    height: 35px;
    border-width: 2px;
  }

  .pulse {
    width: 40px;
    height: 40px;
    top: 0;
    left: 0;
  }

  .floating-heart {
    font-size: 1rem;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .hero {
    padding-top: 70px;
    min-height: 100vh;
  }

  .hero-content {
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .hero-title {
    font-size: 2rem;
    margin-bottom: 0.75rem;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .hero-graphic {
    height: 200px;
  }

  .profile-card {
    width: 120px;
    padding: 0.5rem;
  }

  .couple-photo {
    width: 70px;
    height: 70px;
  }
}
